<execution>
  <constraint>
    ## 工具编排技术约束
    - **集群规模限制**：技术集群25工具，商业集群18工具，学术集群12工具
    - **并行执行约束**：最大支持25个工具同时执行，智能负载均衡
    - **性能评级要求**：每个工具必须有A/B/C性能评级，优先使用A级工具
    - **备选机制约束**：每个核心工具必须有2-3个备选工具
    - **负载均衡限制**：同类工具不能同时大量调用，避免API限制
    - **动态调整限制**：集群配置可动态调整，但需保持核心工具稳定
    - **健康监控约束**：150个工具100%覆盖监控，异常自动降级
    - **结果聚合约束**：多工具结果必须智能聚合，去重率>95%
  </constraint>

  <rule>
    ## 工具编排强制规则
    - **集群自动选择**：根据任务语义特征自动选择对应超级集群
    - **工具优先级执行**：A级工具优先，B级备选，C级应急使用
    - **并行分组强制**：同集群工具智能分为3-5个并行组执行
    - **性能监控强制**：实时监控工具性能，动态调整优先级
    - **备选切换强制**：主工具失败自动切换备选，保证任务完成
    - **效果评估强制**：每次使用后评估工具效果，更新性能评级
    - **组合优化强制**：基于历史数据优化工具组合，提升整体效果
    - **资源管理强制**：智能管理API调用频率，避免触发限制
  </rule>

  <guideline>
    ## 工具编排指导原则
    - **专业化优先**：每个集群专注特定领域，提升专业性
    - **互补性设计**：集群内工具功能互补，覆盖完整工作流
    - **效率最大化**：通过智能组合最大化信息获取效率
    - **质量保证**：多工具交叉验证，确保信息准确性
    - **稳定可靠**：核心工具稳定，备选机制完善
    - **持续优化**：基于使用效果持续优化集群配置
    - **用户友好**：自动化程度高，用户无需了解技术细节
    - **可扩展性**：集群设计支持新工具加入，保持架构灵活
  </guideline>

  <process>
    ## 工具超级集群配置与调度

    ### 🔧 技术情报超级集群 (25工具)
    ```mermaid
    mindmap
      root((技术情报集群))
        GitHub矩阵
          search_repositories_github(A级)
          search_code_github(A级)
          github_search_exa(A级)
          github-api(A级)
        Exa AI专业搜索
          web_search_exa(A级)
          research_paper_search_exa(A级)
          deep_researcher_start_exa(A级)
        文档全覆盖
          get-library-docs_Context_7(A级)
          resolve-library-id_Context_7(A级)
          deepwiki_fetch(A级)
        Firecrawl深度抓取
          firecrawl_search(A级)
          firecrawl_extract(A级)
          firecrawl_deep_research(A级)
        代码库分析
          codebase-retrieval(A级)
          git-commit-retrieval(A级)
          view(A级)
    ```

    **技术集群工具配置**：
    - **GitHub核心组**：search_repositories_github + search_code_github + github_search_exa + github-api
    - **专业搜索组**：web_search_exa + research_paper_search_exa + deep_researcher_start_exa
    - **文档提取组**：get-library-docs_Context_7 + deepwiki_fetch + codebase-retrieval
    - **内容分析组**：firecrawl_search + firecrawl_extract + firecrawl_deep_research
    - **深度研究组**：firecrawl_scrape + git-commit-retrieval + view + sequentialthinking

    ### 💼 商业情报超级集群 (18工具)
    ```mermaid
    mindmap
      root((商业情报集群))
        Exa AI企业研究
          company_research_exa(A级)
          linkedin_search_exa(A级)
          competitor_finder_exa(A级)
        通用网络搜索
          brave_web_search(A级)
          tavily_search(A级)
          web-search(A级)
          web-fetch(A级)
        Firecrawl内容提取
          firecrawl_search(A级)
          firecrawl_extract(A级)
          firecrawl_scrape(A级)
          firecrawl_deep_research(A级)
        思维分析工具
          sequentialthinking(A级)
    ```

    **商业集群工具配置**：
    - **企业调研组**：company_research_exa + linkedin_search_exa + competitor_finder_exa
    - **全网搜索组**：brave_web_search + tavily_search + web-search + firecrawl_search
    - **内容提取组**：firecrawl_extract + tavily_extract + web-fetch + firecrawl_scrape
    - **深度分析组**：firecrawl_deep_research + sequentialthinking + firecrawl_crawl

    ### 📚 学术研究超级集群 (12工具)
    ```mermaid
    mindmap
      root((学术研究集群))
        Exa AI学术搜索
          research_paper_search_exa(A级)
          wikipedia_search_exa(A级)
          deep_researcher_start_exa(A级)
        文档查询转换
          get-library-docs_Context_7(A级)
          resolve-library-id_Context_7(A级)
          deepwiki_fetch(A级)
          convert_to_markdown(A级)
        Firecrawl学术抓取
          firecrawl_search(A级)
          firecrawl_extract(A级)
          firecrawl_deep_research(A级)
        思维分析工具
          sequentialthinking(A级)
    ```

    **学术集群工具配置**：
    - **学术搜索组**：research_paper_search_exa + wikipedia_search_exa + deep_researcher_start_exa
    - **文档处理组**：get-library-docs_Context_7 + deepwiki_fetch + convert_to_markdown
    - **内容提取组**：firecrawl_search + firecrawl_extract + firecrawl_deep_research
    - **验证补充组**：tavily_search + web-search + web-fetch + sequentialthinking

    ## 工具性能评级与动态调整

    ### A级工具 (核心主力)
    - **Exa AI专业搜索**：web_search_exa, research_paper_search_exa, company_research_exa, linkedin_search_exa, github_search_exa, deep_researcher_start_exa
    - **Firecrawl强力抓取**：firecrawl_search, firecrawl_extract, firecrawl_scrape, firecrawl_deep_research
    - **GitHub核心**：search_repositories_github, search_code_github, github-api
    - **文档查询**：get-library-docs_Context_7, resolve-library-id_Context_7, deepwiki_fetch
    - **代码库分析**：codebase-retrieval, git-commit-retrieval, view
    - **通用搜索**：brave_web_search, tavily_search, web-search
    - **思维分析**：sequentialthinking

    ### B级工具 (重要备选)
    - **GitHub扩展**：search_issues_github, get_file_contents_github, list_commits_github, search_users_github
    - **Tavily套件**：tavily_extract, tavily_crawl, tavily_map
    - **Firecrawl扩展**：firecrawl_crawl, firecrawl_map
    - **文档转换**：convert_to_markdown, web-fetch
    - **Exa AI补充**：crawling_exa, wikipedia_search_exa

    ### C级工具 (应急补充)
    - **浏览器操作**：browser_navigate_Playwright, browser_click_Playwright
    - **系统文件**：read_file_Desktop_Commander, write_file_Desktop_Commander
    - **其他专业**：brave_local_search, competitor_finder_exa

    ## 智能调度算法

    ### 工具选择决策矩阵
    ```mermaid
    graph TD
        A[任务输入] --> B[语义分析]
        B --> C{任务类型}
        C -->|技术| D[技术集群权重0.8]
        C -->|商业| E[商业集群权重0.8]
        C -->|学术| F[学术集群权重0.8]
        C -->|综合| G[全集群权重0.6]
        
        D --> H[工具性能评级]
        E --> H
        F --> H
        G --> H
        
        H --> I[历史成功率]
        I --> J[负载均衡检查]
        J --> K[最终工具组合]
    ```

    ### 智能结果聚合系统
    ```mermaid
    flowchart TD
        A[多工具结果] --> B[去重处理]
        B --> C[质量评分]
        C --> D[相似度分析]
        D --> E[信息融合]
        E --> F[可靠性评估]
        F --> G[结构化输出]
    ```

    **聚合算法**：
    1. **去重处理**：基于内容哈希和语义相似度去除重复信息
    2. **质量评分**：准确性(0.4) + 时效性(0.3) + 权威性(0.3)
    3. **相似度分析**：计算信息间语义相似度，识别冲突和互补
    4. **信息融合**：将互补信息融合，冲突信息标注差异
    5. **可靠性评估**：基于多源验证结果评估整体可靠性
    6. **结构化输出**：按重要性排序，生成结构化情报报告

    ### 动态调整机制
    1. **性能监控**：实时监控工具响应时间、成功率、结果质量
    2. **评级更新**：每周根据性能数据更新工具评级
    3. **组合优化**：基于历史数据优化工具组合效果
    4. **负载均衡**：动态调整工具调用频率，避免API限制
    5. **异常处理**：工具失败自动降级，备选工具无缝切换
  </process>

  <criteria>
    ## 工具编排质量标准

    ### 集群配置质量
    - ✅ 技术集群工具数量 25个
    - ✅ 商业集群工具数量 18个
    - ✅ 学术集群工具数量 12个
    - ✅ 工具分类准确率 > 95%
    - ✅ 工具利用率 40% (目标80%+)

    ### 调度效率指标
    - ✅ 集群选择准确率 > 90%
    - ✅ 工具组合优化效果 > 20%
    - ✅ 并行执行成功率 > 95%
    - ✅ 负载均衡效果 > 85%

    ### 工具性能指标
    - ✅ A级工具成功率 > 95%
    - ✅ B级工具成功率 > 90%
    - ✅ C级工具成功率 > 80%
    - ✅ 备选切换成功率 > 95%
    - ✅ 工具健康监控覆盖率 100%

    ### 结果聚合质量
    - ✅ 结果聚合准确率 > 90%
    - ✅ 去重效率 > 95%
    - ✅ 质量评分准确性 > 85%
    - ✅ 信息可靠性 > 90%
  </criteria>
</execution>
