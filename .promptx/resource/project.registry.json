{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-01T05:42:11.548Z", "updatedAt": "2025-08-01T05:42:11.559Z", "resourceCount": 34}, "resources": [{"id": "black-widow", "source": "project", "protocol": "role", "name": "Black Widow 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/black-widow/black-widow.role.md", "metadata": {"createdAt": "2025-08-01T05:42:11.549Z", "updatedAt": "2025-08-01T05:42:11.549Z", "scannedAt": "2025-08-01T05:42:11.549Z", "path": "role/black-widow/black-widow.role.md"}}, {"id": "core-intelligence", "source": "project", "protocol": "execution", "name": "Core Intelligence 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/black-widow/execution/core-intelligence.execution.md", "metadata": {"createdAt": "2025-08-01T05:42:11.549Z", "updatedAt": "2025-08-01T05:42:11.549Z", "scannedAt": "2025-08-01T05:42:11.549Z", "path": "role/black-widow/execution/core-intelligence.execution.md"}}, {"id": "high-density-orchestration", "source": "project", "protocol": "execution", "name": "High Density Orchestration 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/black-widow/execution/high-density-orchestration.execution.md", "metadata": {"createdAt": "2025-08-01T05:42:11.550Z", "updatedAt": "2025-08-01T05:42:11.550Z", "scannedAt": "2025-08-01T05:42:11.550Z", "path": "role/black-widow/execution/high-density-orchestration.execution.md"}}, {"id": "intelligence-workflow", "source": "project", "protocol": "execution", "name": "Intelligence Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/black-widow/execution/intelligence-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T05:42:11.550Z", "updatedAt": "2025-08-01T05:42:11.550Z", "scannedAt": "2025-08-01T05:42:11.550Z", "path": "role/black-widow/execution/intelligence-workflow.execution.md"}}, {"id": "interaction-control", "source": "project", "protocol": "execution", "name": "Interaction Control 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/black-widow/execution/interaction-control.execution.md", "metadata": {"createdAt": "2025-08-01T05:42:11.550Z", "updatedAt": "2025-08-01T05:42:11.550Z", "scannedAt": "2025-08-01T05:42:11.550Z", "path": "role/black-widow/execution/interaction-control.execution.md"}}, {"id": "research-methodology", "source": "project", "protocol": "execution", "name": "Research Methodology 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/black-widow/execution/research-methodology.execution.md", "metadata": {"createdAt": "2025-08-01T05:42:11.550Z", "updatedAt": "2025-08-01T05:42:11.550Z", "scannedAt": "2025-08-01T05:42:11.550Z", "path": "role/black-widow/execution/research-methodology.execution.md"}}, {"id": "result-processing", "source": "project", "protocol": "execution", "name": "Result Processing 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/black-widow/execution/result-processing.execution.md", "metadata": {"createdAt": "2025-08-01T05:42:11.551Z", "updatedAt": "2025-08-01T05:42:11.551Z", "scannedAt": "2025-08-01T05:42:11.551Z", "path": "role/black-widow/execution/result-processing.execution.md"}}, {"id": "risk-analysis", "source": "project", "protocol": "execution", "name": "Risk Analysis 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/black-widow/execution/risk-analysis.execution.md", "metadata": {"createdAt": "2025-08-01T05:42:11.551Z", "updatedAt": "2025-08-01T05:42:11.551Z", "scannedAt": "2025-08-01T05:42:11.551Z", "path": "role/black-widow/execution/risk-analysis.execution.md"}}, {"id": "semantic-tool-selection", "source": "project", "protocol": "execution", "name": "Semantic Tool Selection 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/black-widow/execution/semantic-tool-selection.execution.md", "metadata": {"createdAt": "2025-08-01T05:42:11.551Z", "updatedAt": "2025-08-01T05:42:11.551Z", "scannedAt": "2025-08-01T05:42:11.551Z", "path": "role/black-widow/execution/semantic-tool-selection.execution.md"}}, {"id": "task-management-integration", "source": "project", "protocol": "execution", "name": "Task Management Integration 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/black-widow/execution/task-management-integration.execution.md", "metadata": {"createdAt": "2025-08-01T05:42:11.552Z", "updatedAt": "2025-08-01T05:42:11.552Z", "scannedAt": "2025-08-01T05:42:11.552Z", "path": "role/black-widow/execution/task-management-integration.execution.md"}}, {"id": "tool-health-monitor", "source": "project", "protocol": "execution", "name": "Tool Health Monitor 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/black-widow/execution/tool-health-monitor.execution.md", "metadata": {"createdAt": "2025-08-01T05:42:11.552Z", "updatedAt": "2025-08-01T05:42:11.552Z", "scannedAt": "2025-08-01T05:42:11.552Z", "path": "role/black-widow/execution/tool-health-monitor.execution.md"}}, {"id": "tool-management", "source": "project", "protocol": "execution", "name": "Tool Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/black-widow/execution/tool-management.execution.md", "metadata": {"createdAt": "2025-08-01T05:42:11.553Z", "updatedAt": "2025-08-01T05:42:11.553Z", "scannedAt": "2025-08-01T05:42:11.553Z", "path": "role/black-widow/execution/tool-management.execution.md"}}, {"id": "tool-super-clusters", "source": "project", "protocol": "execution", "name": "Tool Super Clusters 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/black-widow/execution/tool-super-clusters.execution.md", "metadata": {"createdAt": "2025-08-01T05:42:11.553Z", "updatedAt": "2025-08-01T05:42:11.553Z", "scannedAt": "2025-08-01T05:42:11.553Z", "path": "role/black-widow/execution/tool-super-clusters.execution.md"}}, {"id": "intelligence-analysis", "source": "project", "protocol": "thought", "name": "Intelligence Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/black-widow/thought/intelligence-analysis.thought.md", "metadata": {"createdAt": "2025-08-01T05:42:11.553Z", "updatedAt": "2025-08-01T05:42:11.553Z", "scannedAt": "2025-08-01T05:42:11.553Z", "path": "role/black-widow/thought/intelligence-analysis.thought.md"}}, {"id": "pattern-recognition", "source": "project", "protocol": "thought", "name": "Pattern Recognition 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/black-widow/thought/pattern-recognition.thought.md", "metadata": {"createdAt": "2025-08-01T05:42:11.553Z", "updatedAt": "2025-08-01T05:42:11.553Z", "scannedAt": "2025-08-01T05:42:11.553Z", "path": "role/black-widow/thought/pattern-recognition.thought.md"}}, {"id": "risk-assessment", "source": "project", "protocol": "thought", "name": "Risk Assessment 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/black-widow/thought/risk-assessment.thought.md", "metadata": {"createdAt": "2025-08-01T05:42:11.554Z", "updatedAt": "2025-08-01T05:42:11.554Z", "scannedAt": "2025-08-01T05:42:11.554Z", "path": "role/black-widow/thought/risk-assessment.thought.md"}}, {"id": "dialogue-management", "source": "project", "protocol": "execution", "name": "Dialogue Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fury/execution/dialogue-management.execution.md", "metadata": {"createdAt": "2025-08-01T05:42:11.555Z", "updatedAt": "2025-08-01T05:42:11.555Z", "scannedAt": "2025-08-01T05:42:11.555Z", "path": "role/fury/execution/dialogue-management.execution.md"}}, {"id": "fury-workflow", "source": "project", "protocol": "execution", "name": "Fury Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fury/execution/fury-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T05:42:11.555Z", "updatedAt": "2025-08-01T05:42:11.555Z", "scannedAt": "2025-08-01T05:42:11.555Z", "path": "role/fury/execution/fury-workflow.execution.md"}}, {"id": "resume-generation", "source": "project", "protocol": "execution", "name": "Resume Generation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fury/execution/resume-generation.execution.md", "metadata": {"createdAt": "2025-08-01T05:42:11.555Z", "updatedAt": "2025-08-01T05:42:11.555Z", "scannedAt": "2025-08-01T05:42:11.555Z", "path": "role/fury/execution/resume-generation.execution.md"}}, {"id": "fury", "source": "project", "protocol": "role", "name": "Fury 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/fury/fury.role.md", "metadata": {"createdAt": "2025-08-01T05:42:11.555Z", "updatedAt": "2025-08-01T05:42:11.555Z", "scannedAt": "2025-08-01T05:42:11.555Z", "path": "role/fury/fury.role.md"}}, {"id": "agent-broker-mindset", "source": "project", "protocol": "thought", "name": "Agent Broker Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/fury/thought/agent-broker-mindset.thought.md", "metadata": {"createdAt": "2025-08-01T05:42:11.556Z", "updatedAt": "2025-08-01T05:42:11.556Z", "scannedAt": "2025-08-01T05:42:11.556Z", "path": "role/fury/thought/agent-broker-mindset.thought.md"}}, {"id": "value-discovery-techniques", "source": "project", "protocol": "thought", "name": "Value Discovery Techniques 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/fury/thought/value-discovery-techniques.thought.md", "metadata": {"createdAt": "2025-08-01T05:42:11.556Z", "updatedAt": "2025-08-01T05:42:11.556Z", "scannedAt": "2025-08-01T05:42:11.556Z", "path": "role/fury/thought/value-discovery-techniques.thought.md"}}, {"id": "core-management", "source": "project", "protocol": "execution", "name": "Core Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pepper/execution/core-management.execution.md", "metadata": {"createdAt": "2025-08-01T05:42:11.556Z", "updatedAt": "2025-08-01T05:42:11.556Z", "scannedAt": "2025-08-01T05:42:11.556Z", "path": "role/pepper/execution/core-management.execution.md"}}, {"id": "tool-orchestration", "source": "project", "protocol": "execution", "name": "Tool Orchestration 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pepper/execution/tool-orchestration.execution.md", "metadata": {"createdAt": "2025-08-01T05:42:11.557Z", "updatedAt": "2025-08-01T05:42:11.557Z", "scannedAt": "2025-08-01T05:42:11.557Z", "path": "role/pepper/execution/tool-orchestration.execution.md"}}, {"id": "pepper", "source": "project", "protocol": "role", "name": "Pepper 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/pepper/pepper.role.md", "metadata": {"createdAt": "2025-08-01T05:42:11.557Z", "updatedAt": "2025-08-01T05:42:11.557Z", "scannedAt": "2025-08-01T05:42:11.557Z", "path": "role/pepper/pepper.role.md"}}, {"id": "adaptive-learning", "source": "project", "protocol": "thought", "name": "Adaptive Learning 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/pepper/thought/adaptive-learning.thought.md", "metadata": {"createdAt": "2025-08-01T05:42:11.557Z", "updatedAt": "2025-08-01T05:42:11.557Z", "scannedAt": "2025-08-01T05:42:11.557Z", "path": "role/pepper/thought/adaptive-learning.thought.md"}}, {"id": "verification-mindset", "source": "project", "protocol": "thought", "name": "Verification Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/pepper/thought/verification-mindset.thought.md", "metadata": {"createdAt": "2025-08-01T05:42:11.558Z", "updatedAt": "2025-08-01T05:42:11.558Z", "scannedAt": "2025-08-01T05:42:11.558Z", "path": "role/pepper/thought/verification-mindset.thought.md"}}, {"id": "vision-document-management", "source": "project", "protocol": "execution", "name": "Vision Document Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/vision/execution/vision-document-management.execution.md", "metadata": {"createdAt": "2025-08-01T05:42:11.558Z", "updatedAt": "2025-08-01T05:42:11.558Z", "scannedAt": "2025-08-01T05:42:11.558Z", "path": "role/vision/execution/vision-document-management.execution.md"}}, {"id": "vision-enhanced-task-workflow", "source": "project", "protocol": "execution", "name": "Vision Enhanced Task Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/vision/execution/vision-enhanced-task-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T05:42:11.558Z", "updatedAt": "2025-08-01T05:42:11.558Z", "scannedAt": "2025-08-01T05:42:11.558Z", "path": "role/vision/execution/vision-enhanced-task-workflow.execution.md"}}, {"id": "shrimp-task-manager-tools", "source": "project", "protocol": "knowledge", "name": "Shrimp Task Manager Tools 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/vision/knowledge/shrimp-task-manager-tools.knowledge.md", "metadata": {"createdAt": "2025-08-01T05:42:11.559Z", "updatedAt": "2025-08-01T05:42:11.559Z", "scannedAt": "2025-08-01T05:42:11.559Z", "path": "role/vision/knowledge/shrimp-task-manager-tools.knowledge.md"}}, {"id": "zhi-interaction-protocol", "source": "project", "protocol": "knowledge", "name": "Zhi Interaction Protocol 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/vision/knowledge/zhi-interaction-protocol.knowledge.md", "metadata": {"createdAt": "2025-08-01T05:42:11.559Z", "updatedAt": "2025-08-01T05:42:11.559Z", "scannedAt": "2025-08-01T05:42:11.559Z", "path": "role/vision/knowledge/zhi-interaction-protocol.knowledge.md"}}, {"id": "vision-analytical-mind", "source": "project", "protocol": "thought", "name": "Vision Analytical Mind 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/vision/thought/vision-analytical-mind.thought.md", "metadata": {"createdAt": "2025-08-01T05:42:11.559Z", "updatedAt": "2025-08-01T05:42:11.559Z", "scannedAt": "2025-08-01T05:42:11.559Z", "path": "role/vision/thought/vision-analytical-mind.thought.md"}}, {"id": "vision-task-strategy", "source": "project", "protocol": "thought", "name": "Vision Task Strategy 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/vision/thought/vision-task-strategy.thought.md", "metadata": {"createdAt": "2025-08-01T05:42:11.559Z", "updatedAt": "2025-08-01T05:42:11.559Z", "scannedAt": "2025-08-01T05:42:11.559Z", "path": "role/vision/thought/vision-task-strategy.thought.md"}}, {"id": "vision", "source": "project", "protocol": "role", "name": "Vision 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/vision/vision.role.md", "metadata": {"createdAt": "2025-08-01T05:42:11.559Z", "updatedAt": "2025-08-01T05:42:11.559Z", "scannedAt": "2025-08-01T05:42:11.559Z", "path": "role/vision/vision.role.md"}}], "stats": {"totalResources": 34, "byProtocol": {"role": 4, "execution": 19, "thought": 9, "knowledge": 2}, "bySource": {"project": 34}}}